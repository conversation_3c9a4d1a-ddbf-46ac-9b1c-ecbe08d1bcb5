package main

import (
	"aps/lib/amplifin/utils"
	"aps/services/ozow/assets"
	"aps/services/ozow/internal/handlers"
	"log"
	"net/http"
	"os"
	"strings"

	"github.com/joho/godotenv"
)

func init() {
	err := godotenv.Load(".env")
	if err != nil {
		log.Println("Prod environment")
	}
}

func fixRoute(route string) string {
	if len(route) > 4 && route[3] == ' ' && route[4] != '/' {
		return route[:4] + "/" + route[4:]
	}
	if len(route) > 5 && route[4] == ' ' && route[5] != '/' {
		return route[:5] + "/" + route[5:]
	}
	return route
}

func main() {
	URL := os.Getenv("URL")
	if URL == "" {
		URL = "" // default value - no prefix
	}
	// Ensure URL doesn't start with / for proper route construction
	if strings.HasPrefix(URL, "/") {
		URL = URL[1:]
	}
	mux := http.NewServeMux()

	//CSS and JS to be cached
	var cssHandler, jsHandler http.Handler
	if URL == "" {
		// No prefix, serve directly
		cssHandler = http.FileServer(http.FS(assets.CSS))
		jsHandler = http.FileServer(http.FS(assets.JS))
	} else {
		// With prefix, strip the prefix
		cssHandler = http.StripPrefix("/"+URL, http.FileServer(http.FS(assets.CSS)))
		jsHandler = http.StripPrefix("/"+URL, http.FileServer(http.FS(assets.JS)))
	}

	for path, handler := range map[string]http.Handler{
		fixRoute(utils.AddPre(utils.AddPre("/css/", URL), "GET ")): cssHandler,
		fixRoute(utils.AddPre(utils.AddPre("/js/", URL), "GET ")):  jsHandler,
	} {
		mux.Handle(path, utils.CacheHandler(handler))
		log.Println("Registered handler for path", path)
	}
	//

	// Authentication routes
	loginGetRoute := fixRoute(utils.AddPre(utils.AddPre("/login", URL), "GET "))
	loginPostRoute := fixRoute(utils.AddPre(utils.AddPre("/login", URL), "POST "))
	logoutRoute := fixRoute(utils.AddPre(utils.AddPre("/logout", URL), "POST "))

	log.Println("Registered handler for path", loginGetRoute)
	log.Println("Registered handler for path", loginPostRoute)
	log.Println("Registered handler for path", logoutRoute)

	mux.HandleFunc(loginGetRoute, handlers.LoginHandler)
	mux.HandleFunc(loginPostRoute, handlers.LoginPostHandler)
	mux.HandleFunc(logoutRoute, handlers.LogoutHandler)

	for path, handler := range map[string]http.HandlerFunc{
		fixRoute(utils.AddPre(utils.AddPre("/PaymentRequest", URL), "POST ")): handlers.CreatePaymentRequest,
		fixRoute(utils.AddPre(utils.AddPre("/Refund", URL),
			"POST ")): handlers.SubmitRefund,
		fixRoute(utils.AddPre(utils.AddPre("/Notify", URL), "POST ")): handlers.Notify,

		//pages
		fixRoute(utils.AddPre(utils.AddPre("/pay", URL), "POST ")):    handlers.WebPaymentRequest,
		fixRoute(utils.AddPre(utils.AddPre("/success", URL), "GET ")): handlers.Success,
		fixRoute(utils.AddPre(utils.AddPre("/cancel", URL), "GET ")):  handlers.Cancel,
		fixRoute(utils.AddPre(utils.AddPre("/error", URL), "GET ")):   handlers.Error,
		fixRoute(utils.AddPre(utils.AddPre("/home", URL), "GET ")):    handlers.Index,
		fixRoute(utils.AddPre(utils.AddPre("/reset", URL), "GET ")):   handlers.Reset,
	} {
		mux.HandleFunc(path, handler)
		log.Println("Registered handler for path", path)
	}

	// Root route - redirect to /home
	mux.HandleFunc("GET /", func(w http.ResponseWriter, r *http.Request) {
		if URL == "" {
			http.Redirect(w, r, "/home", http.StatusFound)
		} else {
			http.Redirect(w, r, "/"+URL+"/home", http.StatusFound)
		}
	})

	// Handle URL prefix route if URL is not empty
	if URL != "" {
		mux.HandleFunc("GET /"+URL, func(w http.ResponseWriter, r *http.Request) {
			http.Redirect(w, r, "/"+URL+"/home", http.StatusFound)
		})
	}

	err := http.ListenAndServe(":3000", mux)
	log.Println("Starting server on port 3000")
	if err != nil {
		log.Fatal(err)
	}

}
