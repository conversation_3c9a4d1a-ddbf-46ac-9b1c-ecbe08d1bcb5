package logging

import (
	"aps/services/ozow/internal/models"
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"os"
	"strconv"
	"time"

	"github.com/go-redis/redis/v8"
)

type RedisLogger struct {
	client *redis.Client
}

func NewRedisLogger() *RedisLogger {
	redisAddr := os.Getenv("REDIS_ADDR")
	redisPassword := os.Getenv("REDIS_PASSWORD")
	redisDBStr := os.Getenv("REDIS_DB")

	if redisAddr == "" {
		redisAddr = "localhost:6379"
	}
	redisDB := 0
	if redisDBStr != "" {
		if db, err := strconv.Atoi(redisDBStr); err == nil {
			redisDB = db
		} else {
			log.Printf("Invalid REDIS_DB value '%s', using default DB 0", redisDBStr)
		}
	}

	rdb := redis.NewClient(&redis.Options{
		Addr:     redisAddr,
		Password: redisPassword,
		DB:       redisDB,
	})

	ctx := context.Background()
	_, err := rdb.Ping(ctx).Result()
	if err != nil {
		log.Printf("Warning: Could not connect to Redis for logging: %v", err)
		log.Println("Logging will not work properly without Redis")
	} else {
		log.Println("Successfully connected to Redis for logging")
	}

	return &RedisLogger{client: rdb}
}

// LogLoginAttempt logs a login attempt to Redis
func (rl *RedisLogger) LogLoginAttempt(idNumber string, success bool, r *http.Request, errorReason string) {
	if rl.client == nil {
		log.Println("Redis client not available for logging")
		return
	}

	attempt := models.LoginAttempt{
		IDNumber:    idNumber,
		Success:     success,
		Timestamp:   time.Now(),
		IPAddress:   getClientIP(r),
		UserAgent:   r.UserAgent(),
		ErrorReason: errorReason,
	}

	data, err := json.Marshal(attempt)
	if err != nil {
		log.Printf("Error marshaling login attempt: %v", err)
		return
	}

	ctx := context.Background()
	key := fmt.Sprintf("login_attempts:%s:%d", idNumber, time.Now().Unix())
	
	// Store the login attempt with 30 days expiration
	err = rl.client.Set(ctx, key, data, 30*24*time.Hour).Err()
	if err != nil {
		log.Printf("Error storing login attempt in Redis: %v", err)
		return
	}

	// Also add to a list for easy retrieval
	listKey := fmt.Sprintf("login_attempts_list:%s", idNumber)
	err = rl.client.LPush(ctx, listKey, data).Err()
	if err != nil {
		log.Printf("Error adding login attempt to list: %v", err)
	}

	// Keep only last 100 attempts per user
	rl.client.LTrim(ctx, listKey, 0, 99)
	rl.client.Expire(ctx, listKey, 30*24*time.Hour)

	log.Printf("Logged login attempt for user %s: success=%t", idNumber, success)
}

// LogPayment logs a payment event to Redis
func (rl *RedisLogger) LogPayment(userID, contractKey string, amount float64, transactionRef, paymentRequestID, status string, r *http.Request, errorMessage string) {
	if rl.client == nil {
		log.Println("Redis client not available for logging")
		return
	}

	paymentLog := models.PaymentLog{
		UserID:           userID,
		ContractKey:      contractKey,
		Amount:           amount,
		TransactionRef:   transactionRef,
		PaymentRequestID: paymentRequestID,
		Status:           status,
		Timestamp:        time.Now(),
		IPAddress:        getClientIP(r),
		ErrorMessage:     errorMessage,
	}

	data, err := json.Marshal(paymentLog)
	if err != nil {
		log.Printf("Error marshaling payment log: %v", err)
		return
	}

	ctx := context.Background()
	key := fmt.Sprintf("payments:%s:%d", userID, time.Now().Unix())
	
	// Store the payment log with 90 days expiration
	err = rl.client.Set(ctx, key, data, 90*24*time.Hour).Err()
	if err != nil {
		log.Printf("Error storing payment log in Redis: %v", err)
		return
	}

	// Also add to a list for easy retrieval
	listKey := fmt.Sprintf("payments_list:%s", userID)
	err = rl.client.LPush(ctx, listKey, data).Err()
	if err != nil {
		log.Printf("Error adding payment log to list: %v", err)
	}

	// Keep only last 200 payments per user
	rl.client.LTrim(ctx, listKey, 0, 199)
	rl.client.Expire(ctx, listKey, 90*24*time.Hour)

	log.Printf("Logged payment for user %s: amount=%.2f, status=%s", userID, amount, status)
}

// GetLoginAttempts retrieves recent login attempts for a user
func (rl *RedisLogger) GetLoginAttempts(idNumber string, limit int) ([]models.LoginAttempt, error) {
	if rl.client == nil {
		return nil, fmt.Errorf("Redis client not available")
	}

	ctx := context.Background()
	listKey := fmt.Sprintf("login_attempts_list:%s", idNumber)
	
	if limit <= 0 {
		limit = 10
	}

	results, err := rl.client.LRange(ctx, listKey, 0, int64(limit-1)).Result()
	if err != nil {
		return nil, fmt.Errorf("error retrieving login attempts: %w", err)
	}

	attempts := make([]models.LoginAttempt, 0, len(results))
	for _, result := range results {
		var attempt models.LoginAttempt
		if err := json.Unmarshal([]byte(result), &attempt); err != nil {
			log.Printf("Error unmarshaling login attempt: %v", err)
			continue
		}
		attempts = append(attempts, attempt)
	}

	return attempts, nil
}

// GetPaymentLogs retrieves recent payment logs for a user
func (rl *RedisLogger) GetPaymentLogs(userID string, limit int) ([]models.PaymentLog, error) {
	if rl.client == nil {
		return nil, fmt.Errorf("Redis client not available")
	}

	ctx := context.Background()
	listKey := fmt.Sprintf("payments_list:%s", userID)
	
	if limit <= 0 {
		limit = 10
	}

	results, err := rl.client.LRange(ctx, listKey, 0, int64(limit-1)).Result()
	if err != nil {
		return nil, fmt.Errorf("error retrieving payment logs: %w", err)
	}

	logs := make([]models.PaymentLog, 0, len(results))
	for _, result := range results {
		var paymentLog models.PaymentLog
		if err := json.Unmarshal([]byte(result), &paymentLog); err != nil {
			log.Printf("Error unmarshaling payment log: %v", err)
			continue
		}
		logs = append(logs, paymentLog)
	}

	return logs, nil
}

// getClientIP extracts the client IP address from the request
func getClientIP(r *http.Request) string {
	// Check X-Forwarded-For header first
	forwarded := r.Header.Get("X-Forwarded-For")
	if forwarded != "" {
		return forwarded
	}

	// Check X-Real-IP header
	realIP := r.Header.Get("X-Real-IP")
	if realIP != "" {
		return realIP
	}

	// Fall back to RemoteAddr
	return r.RemoteAddr
}
