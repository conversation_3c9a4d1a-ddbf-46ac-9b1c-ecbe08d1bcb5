package handlers

import (
	"aps/services/ozow/internal/components"
	"aps/services/ozow/internal/views"
	"log"
	"net/http"
)

// func Index(w http.ResponseWriter, r *http.Request) {
// 	err := views.Index().Render(r.Context(), w)
// 	if err != nil {
// 		log.Println(err)
// 	}
// 	return
// }

func Index(w http.ResponseWriter, r *http.Request) {
	user, err := GetCurrentUser(r)
	if err != nil {
		log.Printf("Error getting current user: %v", err)
		http.Error(w, "Internal server error", http.StatusInternalServerError)
		return
	}

	if user != nil {
		err = views.IndexWithUser(user).Render(r.Context(), w)
		if err != nil {
			log.Printf("Error rendering index with user: %v", err)
			http.Error(w, "Internal server error", http.StatusInternalServerError)
		}
		return
	}

	http.Redirect(w, r, "/login", http.StatusFound)
}

func Success(w http.ResponseWriter, r *http.Request) {
	err := views.Success().Render(r.Context(), w)
	if err != nil {
		log.Println(err)
	}

}

func Cancel(w http.ResponseWriter, r *http.Request) {
	err := views.Cancel().Render(r.Context(), w)
	if err != nil {
		log.Println(err)
	}
	return
}

func Error(w http.ResponseWriter, r *http.Request) {
	err := views.Error().Render(r.Context(), w)
	if err != nil {
		log.Println(err)
	}
	return
}

func Reset(w http.ResponseWriter, r *http.Request) {
	err := components.Form().Render(r.Context(), w)
	if err != nil {
		log.Println(err)
	}
}
