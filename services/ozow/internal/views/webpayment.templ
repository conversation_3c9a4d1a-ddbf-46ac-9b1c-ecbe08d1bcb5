package views

import (
	"aps/services/ozow/internal/components"
	"aps/services/ozow/internal/models"
)

const (
	page         = "Pay"
	style        = "/ozow/css/style.css"
	payment_link = "/ozow/pay"
)

templ Index() {
	<!DOCTYPE html>
	<html lang="en" data-theme="light">
		<head>
			<meta charset="UTF-8"/>
			<meta name="viewport" content="width=device-width, initial-scale=1.0"/>
			<title>Pay</title>
			<link
				rel="stylesheet"
				type="text/css"
				href={ models.STYLE }
			/>
			<link rel="icon" href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22><text y=%22.9em%22 font-size=%2290%22>💳</text></svg>"/>
		</head>
		<body>
			<script type="module" src={ models.JS }></script>
			@components.Header(page)
			<div class="flex min-h-screen items-center justify-center bg-opacity-80">
				<div class="card glass w-96 pt-6">
					<div class="items-center justify-center  text-center text-3xl">
						🪙
					</div>
					<div class="card-body grid place-items-center">
						<form
							id="payment-form"
							hx-post={ payment_link }
							hx-target="#toast"
							hx-indicator="#indicator"
							hx-swap="innerHTML"
							class="grid place-items-center"
						>
							<div id="toast" class="card-body place-items-center w-80">
								<h2 class="card-title pb-3">Make Payment</h2>
								<input name="idNumber" id="idNumber" type="text" placeholder="ID Number" class="input focus:outline-none"/>
								<input name="amount" id="amount" type="number" placeholder="Amount" class="input focus:outline-none"/>
							</div>
							<button
								type="submit"
								class="content-center btn btn-primary btn-wide"
							>
								Pay
							</button>
							<span id="indicator" class="icon-[line-md--loading-twotone-loop] text-2xl htmx-indicator"></span>
						</form>
					</div>
				</div>
			</div>
		</body>
	</html>
}

templ IndexWithUser(user *models.User) {
	<!DOCTYPE html>
	<html lang="en">
		<head>
			<meta charset="UTF-8"/>
			<meta name="viewport" content="width=device-width, initial-scale=1.0"/>
			<title>Pay - Payment Interface</title>
			<style>
				* {
					margin: 0;
					padding: 0;
					box-sizing: border-box;
				}

				:root {
					--bg-primary: #f8fafc;
					--bg-secondary: white;
					--text-primary: #1f2937;
					--text-secondary: #6b7280;
					--border-color: #e5e7eb;
					--accent-color: #6366f1;
				}

				[data-theme="dark"] {
					--bg-primary: #0f172a;
					--bg-secondary: #1e293b;
					--text-primary: #f1f5f9;
					--text-secondary: #94a3b8;
					--border-color: #334155;
					--accent-color: #818cf8;
				}

				body {
					font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
					background: var(--bg-primary);
					color: var(--text-primary);
					min-height: 100vh;
					transition: background-color 0.3s, color 0.3s;
				}

				/* Header */
				.header {
					display: flex;
					justify-content: space-between;
					align-items: center;
					padding: 16px 32px;
					background: var(--bg-secondary);
					border-bottom: 1px solid var(--border-color);
					box-shadow: 0 1px 3px rgba(0,0,0,0.1);
				}

				.logo {
					font-size: 24px;
					font-weight: 700;
					color: var(--text-primary);
				}

				.welcome {
					font-size: 16px;
					color: var(--text-secondary);
				}

				.user-controls {
					display: flex;
					gap: 4px;
				}

				.user-controls button {
					display: flex;
					align-items: center;
					gap: 6px;
					padding: 10px 14px;
					border: none;
					background: transparent;
					color: var(--text-secondary);
					border-radius: 8px;
					font-size: 14px;
					cursor: pointer;
					transition: all 0.2s;
				}

				.user-controls button:hover {
					background: var(--bg-primary);
					color: var(--text-primary);
				}

				.user-controls .logout:hover {
					background: #fef2f2;
					color: #dc2626;
				}

				[data-theme="dark"] .user-controls .logout:hover {
					background: #7f1d1d;
					color: #fca5a5;
				}

				.icon {
					width: 16px;
					height: 16px;
					fill: currentColor;
				}



				/* Main Content */
				.main-content {
					display: flex;
					flex-direction: column;
					align-items: center;
					padding: 80px 32px;
					max-width: 500px;
					margin: 0 auto;
				}

				/* User Avatar */
				.user-avatar {
					width: 80px;
					height: 80px;
					border-radius: 50%;
					background: var(--accent-color);
					display: flex;
					align-items: center;
					justify-content: center;
					color: white;
					font-size: 28px;
					font-weight: 600;
					margin-bottom: 16px;
					box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
				}

				.welcome-title {
					font-size: 24px;
					font-weight: 600;
					color: var(--text-primary);
					margin-bottom: 8px;
				}

				.user-subtitle {
					color: var(--text-secondary);
					margin-bottom: 48px;
				}

				/* Payment Card */
				.payment-card {
					background: var(--bg-secondary);
					border-radius: 12px;
					padding: 32px;
					box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
					border: 1px solid var(--border-color);
					width: 100%;
					max-width: 400px;
				}

				.payment-header {
					display: flex;
					align-items: center;
					gap: 12px;
					margin-bottom: 24px;
				}

				.payment-icon {
					width: 20px;
					height: 20px;
					fill: var(--accent-color);
				}

				.payment-title {
					font-size: 18px;
					font-weight: 600;
					color: var(--text-primary);
				}

				.form-group {
					margin-bottom: 20px;
				}

				.form-label {
					display: block;
					font-size: 14px;
					font-weight: 500;
					color: var(--text-primary);
					margin-bottom: 6px;
				}

				.input-wrapper {
					position: relative;
				}

				.form-input {
					width: 100%;
					padding: 12px 16px;
					border: 1px solid var(--border-color);
					border-radius: 8px;
					font-size: 16px;
					background: var(--bg-secondary);
					color: var(--text-primary);
					transition: border-color 0.2s;
				}

				.form-input:focus {
					outline: none;
					border-color: var(--accent-color);
				}

				.form-input:read-only {
					background: var(--bg-primary);
					color: var(--text-secondary);
				}

				.verified-badge {
					position: absolute;
					right: 12px;
					top: 50%;
					transform: translateY(-50%);
					background: #10b981;
					color: white;
					font-size: 11px;
					padding: 2px 6px;
					border-radius: 4px;
					font-weight: 500;
				}

				.amount-input {
					font-size: 18px;
					text-align: center;
					font-weight: 500;
				}

				.minimum-note {
					font-size: 12px;
					color: var(--text-secondary);
					margin-top: 4px;
				}

				.button-group {
					display: flex;
					gap: 12px;
					margin-top: 24px;
				}

				.btn {
					flex: 1;
					padding: 14px 20px;
					border: none;
					border-radius: 8px;
					font-size: 14px;
					font-weight: 500;
					cursor: pointer;
					transition: all 0.2s;
				}

				.btn-primary {
					background: var(--accent-color);
					color: white;
				}

				.btn-primary:hover {
					background: #5855eb;
				}

				.btn-secondary {
					background: transparent;
					color: var(--text-secondary);
					border: 1px solid var(--border-color);
				}

				.btn-secondary:hover {
					background: var(--bg-primary);
				}

				.loading-indicator {
					display: none;
					text-align: center;
					padding: 20px;
					color: var(--text-secondary);
				}

				.loading-indicator.show {
					display: block;
				}

				/* Logout Popup */
				.logout-popup {
					position: fixed;
					top: 0;
					left: 0;
					right: 0;
					bottom: 0;
					background: rgba(0, 0, 0, 0.5);
					display: none;
					align-items: center;
					justify-content: center;
					z-index: 1000;
				}

				.logout-popup.show {
					display: flex;
				}

				.logout-popup-content {
					background: var(--bg-secondary);
					border-radius: 12px;
					padding: 24px;
					max-width: 400px;
					width: 90%;
					box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
					border: 1px solid var(--border-color);
				}

				.logout-popup h3 {
					font-size: 18px;
					font-weight: 600;
					color: var(--text-primary);
					margin-bottom: 8px;
				}

				.logout-popup p {
					color: var(--text-secondary);
					margin-bottom: 20px;
				}

				.logout-popup-buttons {
					display: flex;
					gap: 12px;
				}

				.logout-popup-buttons button {
					flex: 1;
					padding: 12px 16px;
					border: none;
					border-radius: 8px;
					font-size: 14px;
					font-weight: 500;
					cursor: pointer;
					transition: all 0.2s;
				}

				.logout-cancel {
					background: transparent;
					color: var(--text-secondary);
					border: 1px solid var(--border-color);
				}

				.logout-cancel:hover {
					background: var(--bg-primary);
				}

				.logout-confirm {
					background: #dc2626;
					color: white;
				}

				.logout-confirm:hover {
					background: #b91c1c;
				}

				@media (max-width: 768px) {
					.header {
						padding: 12px 16px;
					}

					.welcome {
						display: none;
					}

					.main-content {
						padding: 40px 16px;
					}

					.payment-card {
						padding: 24px;
					}
				}
			</style>
			<link rel="icon" href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22><text y=%22.9em%22 font-size=%2290%22>💳</text></svg>"/>
		</head>
		<body>
			<script type="module" src={ models.JS }></script>

			<div class="header">
				<div class="logo">Pay</div>
				<div class="welcome">Welcome, { user.FullName }</div>
				<div class="user-controls">
					<button>
						<svg class="icon" viewBox="0 0 24 24">
							<path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
						</svg>
						Profile
					</button>
					<button onclick="toggleTheme()">
						<svg class="icon" viewBox="0 0 24 24">
							<path d="M12,15.5A3.5,3.5 0 0,1 8.5,12A3.5,3.5 0 0,1 12,8.5A3.5,3.5 0 0,1 15.5,12A3.5,3.5 0 0,1 12,15.5M19.43,12.97C19.47,12.65 19.5,12.33 19.5,12C19.5,11.67 19.47,11.34 19.43,11.03L21.54,9.37C21.73,9.22 21.78,8.95 21.66,8.73L19.66,5.27C19.54,5.05 19.27,4.96 19.05,5.05L16.56,6.05C16.04,5.66 15.5,5.32 14.87,5.07L14.5,2.42C14.46,2.18 14.25,2 14,2H10C9.75,2 9.54,2.18 9.5,2.42L9.13,5.07C8.5,5.32 7.96,5.66 7.44,6.05L4.95,5.05C4.73,4.96 4.46,5.05 4.34,5.27L2.34,8.73C2.22,8.95 2.27,9.22 2.46,9.37L4.57,11.03C4.53,11.34 4.5,11.67 4.5,12C4.5,12.33 4.53,12.65 4.57,12.97L2.46,14.63C2.27,14.78 2.22,15.05 2.34,15.27L4.34,18.73C4.46,18.95 4.73,19.03 4.95,18.95L7.44,17.94C7.96,18.34 8.5,18.68 9.13,18.93L9.5,21.58C9.54,21.82 9.75,22 10,22H14C14.25,22 14.46,21.82 14.5,21.58L14.87,18.93C15.5,18.68 16.04,18.34 16.56,17.94L19.05,18.95C19.27,19.03 19.54,18.95 19.66,18.73L21.66,15.27C21.78,15.05 21.73,14.78 21.54,14.63L19.43,12.97Z"/>
						</svg>
						<span id="theme-text">Dark Mode</span>
						<span id="theme-icon">🌙</span>
					</button>
					<button class="logout" onclick="showLogoutPopup()">
						<svg class="icon" viewBox="0 0 24 24">
							<path d="M16,17V14H9V10H16V7L21,12L16,17M14,2A2,2 0 0,1 16,4V6H14V4H5V20H14V18H16V20A2,2 0 0,1 14,22H5A2,2 0 0,1 3,20V4A2,2 0 0,1 5,2H14Z"/>
						</svg>
						Logout
					</button>
				</div>
			</div>

			<!-- Logout Popup -->
			<div id="logout-popup" class="logout-popup">
				<div class="logout-popup-content">
					<h3>Confirm Logout</h3>
					<p>Are you sure you want to logout? This will end your secure session.</p>
					<div class="logout-popup-buttons">
						<button class="logout-cancel" onclick="hideLogoutPopup()">Cancel</button>
						<button class="logout-confirm" onclick="confirmLogout()">Logout</button>
					</div>
				</div>
			</div>

			<div class="main-content">
				<div class="user-avatar">{ string([]rune(user.FirstName)[0]) }{ string([]rune(user.Surname)[0]) }</div>
				<h1 class="welcome-title">Welcome, { user.FirstName }!</h1>
				if user.Employer != "" {
					<p class="user-subtitle">{ user.Employer }</p>
				}

				<div class="payment-card">
					<div class="payment-header">
						<svg class="payment-icon" viewBox="0 0 24 24">
							<path d="M20,8H4V6H20M20,18H4V12H20M20,4H4C2.89,4 2,4.89 2,6V18A2,2 0 0,0 4,20H20A2,2 0 0,0 22,18V6C22,4.89 21.11,4 20,4Z"/>
						</svg>
						<h2 class="payment-title">Make Payment</h2>
					</div>

					<form
						id="payment-form"
						hx-post={ payment_link }
						hx-target="#payment-result"
						hx-indicator="#loading-indicator"
						hx-swap="innerHTML"
					>
						<div class="form-group">
							<label class="form-label" for="idNumber">ID Number ✓ Verified</label>
							<input
								type="text"
								id="idNumber"
								name="idNumber"
								class="form-input"
								value={ user.IdentificationDetail }
								readonly
							/>
						</div>

						<div class="form-group">
							<label class="form-label" for="amount">Payment Amount</label>
							<input
								type="number"
								id="amount"
								name="amount"
								class="form-input amount-input"
								placeholder="0.00"
								min="50.00"
								step="0.01"
								required
							/>
							<div class="minimum-note">Minimum: R50.00</div>
						</div>

						<div class="button-group">
							<button type="button" class="btn btn-secondary" onclick="clearAmount()">Clear Amount</button>
							<button type="submit" class="btn btn-primary">Process Payment</button>
						</div>
					</form>

					<div id="loading-indicator" class="loading-indicator htmx-indicator">
						Processing payment...
					</div>

					<div id="payment-result"></div>
				</div>
			</div>

			<script>
				// Theme toggle functionality
				function toggleTheme() {
					const html = document.documentElement;
					const currentTheme = html.getAttribute('data-theme');
					const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

					html.setAttribute('data-theme', newTheme);
					localStorage.setItem('theme', newTheme);
					updateThemeButton(newTheme);
				}

				// Update theme button text and icon
				function updateThemeButton(theme) {
					const themeText = document.getElementById('theme-text');
					const themeIcon = document.getElementById('theme-icon');

					// Only update if elements exist (they're in the header dropdown)
					if (themeText && themeIcon) {
						if (theme === 'dark') {
							themeText.textContent = 'Light Mode';
							themeIcon.textContent = '☀️';
						} else {
							themeText.textContent = 'Dark Mode';
							themeIcon.textContent = '🌙';
						}
					}
				}

				// Load saved theme on page load
				function loadTheme() {
					const savedTheme = localStorage.getItem('theme') || 'light';
					document.documentElement.setAttribute('data-theme', savedTheme);
					updateThemeButton(savedTheme);
				}

				// Logout popup functions
				function showLogoutPopup() {
					document.getElementById('logout-popup').classList.add('show');
				}

				function hideLogoutPopup() {
					document.getElementById('logout-popup').classList.remove('show');
				}

				function confirmLogout() {
					// Create a form and submit it for logout
					const form = document.createElement('form');
					form.method = 'POST';
					form.action = '/ozow/logout';
					form.style.display = 'none';
					document.body.appendChild(form);
					form.submit();
				}

				function clearAmount() {
					document.getElementById('amount').value = '';
				}

				// Initialize theme on page load
				document.addEventListener('DOMContentLoaded', loadTheme);

				// Close popup when clicking outside
				document.getElementById('logout-popup').addEventListener('click', function(e) {
					if (e.target === this) {
						hideLogoutPopup();
					}
				});
			</script>
		</body>
	</html>
}