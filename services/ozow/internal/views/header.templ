package views

import "aps/services/ozow/internal/models"

templ Header(page string) {
	<header class="navbar bg-base-100 shadow-lg">
		<div class="navbar-start">
			<a class="btn btn-ghost normal-case text-xl" href="/">
				{ page }
			</a>
		</div>
		<div class="navbar-end">
			<a class="btn btn-primary" href="/login">
				Login
			</a>
		</div>
	</header>
}

templ HeaderWithUser(page string, user *models.User) {
	<header class="navbar bg-base-100 shadow-lg">
		<div class="navbar-start">
			<a class="btn btn-ghost normal-case text-xl" href="/ozow">
				{ page }
			</a>
		</div>
		<div class="navbar-center">
			<span class="text-sm">Welcome, { user.FullName }</span>
		</div>
		<div class="navbar-end">
			<div class="dropdown dropdown-end">
				<div tabindex="0" role="button" class="btn btn-ghost btn-circle avatar">
					<div class="w-10 rounded-full bg-primary text-primary-content flex items-center justify-center">
						<span class="text-sm font-bold">
							{ string([]rune(user.FirstName)[0]) }{ string([]rune(user.Surname)[0]) }
						</span>
					</div>
				</div>
				<ul tabindex="0" class="mt-3 z-[1] p-2 shadow menu menu-sm dropdown-content bg-base-100 rounded-box w-52">
					<li>
						<a class="justify-between">
							Profile
							<span class="badge">New</span>
						</a>
					</li>
					<li>
						<a id="theme-toggle-btn" onclick="toggleTheme()" class="justify-between">
							<span id="theme-text">Dark Mode</span>
							<span id="theme-icon">🌙</span>
						</a>
					</li>
					<li>
						<button hx-post="/ozow/logout" hx-confirm="Are you sure you want to logout?">
							Logout
						</button>
					</li>
				</ul>
			</div>
		</div>
	</header>
}