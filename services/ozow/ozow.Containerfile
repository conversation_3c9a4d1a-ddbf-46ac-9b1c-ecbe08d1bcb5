# Use a minimal base image
FROM docker.io/golang:1.23 AS builder

ENV CGO_ENABLED=0
ENV GOOS=linux

WORKDIR /app

COPY go.mod go.sum ./

COPY lib/ ./lib/

COPY services/ozow/ ./services/ozow/

RUN go mod download && go mod tidy  && go mod verify

RUN go build -v -ldflags "-w -s" -o /usr/local/bin/app ./services/ozow/cmd/ozow-app/ozow.go

# Create a minimal final image
FROM gcr.io/distroless/static-debian12

# REDIS configuration
ENV REDIS_ADDR="129.232.241.242:31268"
ENV REDIS_DB="1"
ENV REDIS_PASSWORD="1gU6I96YhP"

# Ozow API configuration
ENV Ozow_API="https://api.ozow.com"

# Vault configuration
ENV VAULT_URL="http://rubixgateway.capetown:8200"
ENV VAULT_PATH="/v1/cubbyhole/cred"
ENV VAULT_TOKEN="hvs.CAESICheCP5riG_Y7El_vq6h597VIzYPwi4VKu79HmhHn6_jGh4KHGh2cy5uTWRYY3I2blFTcHc3MmhvbGxYTjIyMGE"
ENV VAULT_AUTH="/v1/cubbyhole/auth_token"

# Rubix configuration
ENV RUBIX="https://rubixcollect.co.za/api/trustlink.php"
ENV RUBIX_TOKEN="1CVSdYnnlDVuj65rcU6P!D9Fk1pgS=3cuxLk"

# Database configuration
ENV DBHOSTNAME="************:3306"
ENV DBNAME="blue_label_reporting"
ENV DBPASS="Jek1oleibo"
ENV DBUSER="tanitap"

ENV URL="/ozow"
ENV SITE_CODE="BLU-RUB-001"

COPY --from=builder /usr/local/bin/app /usr/local/bin/app

EXPOSE 3000
CMD ["app"]